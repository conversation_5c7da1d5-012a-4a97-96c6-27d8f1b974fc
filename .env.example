# LangExtract API Configuration
# Replace with your actual API key from your LangExtract provider
LANGEXTRACT_API_KEY=your_api_key_here

# Model Configuration (optional - defaults to gemini-2.5-flash)
# LANGEXTRACT_MODEL_ID=gemini-2.5-flash

# Application Configuration
# LOG_LEVEL=INFO
# MAX_EXTRACTION_PASSES=1

# File Paths (optional - can be overridden via command line)
# INPUT_CSV_PATH=/data/input/products.csv
# OUTPUT_CSV_PATH=/data/output/extracted_variants.csv
# PRODUCT_COLUMN_NAME=product_name
