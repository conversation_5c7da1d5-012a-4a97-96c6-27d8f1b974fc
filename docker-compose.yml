version: '3.8'

services:
  langextract-app:
    build: .
    container_name: langextract-product-extractor
    environment:
      - LANGEXTRACT_API_KEY=${LANGEXTRACT_API_KEY}
      - LANGEXTRACT_MODEL_ID=${LANGEXTRACT_MODEL_ID:-gemini-2.5-flash}
    volumes:
      # Mount input CSV files
      - ./data/input:/data/input:ro
      # Mount output directory
      - ./data/output:/data/output:rw
      # Mount logs directory
      - ./logs:/app/logs:rw
    command: [
      "--input-csv", "/data/input/products.csv",
      "--output-csv", "/data/output/extracted_variants.csv",
      "--column-name", "product_name"
    ]
    restart: "no"

  # Alternative service for custom runs
  langextract-app-interactive:
    build: .
    container_name: langextract-product-extractor-interactive
    environment:
      - LANGEXTRACT_API_KEY=${LANGEXTRACT_API_KEY}
      - LANGEXTRACT_MODEL_ID=${LANGEXTRACT_MODEL_ID:-gemini-2.5-flash}
    volumes:
      - ./data/input:/data/input:ro
      - ./data/output:/data/output:rw
      - ./logs:/app/logs:rw
    stdin_open: true
    tty: true
    entrypoint: ["/bin/bash"]
    profiles: ["interactive"]
