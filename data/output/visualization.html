<style>
.lx-highlight { position: relative; border-radius:3px; padding:1px 2px;}
.lx-highlight .lx-tooltip {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  background: #333;
  color: #fff;
  text-align: left;
  border-radius: 4px;
  padding: 6px 8px;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  max-width: 240px;
  white-space: normal;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
.lx-highlight:hover .lx-tooltip { visibility: visible; opacity:1; }
.lx-animated-wrapper { max-width: 100%; font-family: Arial, sans-serif; }
.lx-controls {
  background: #fafafa; border: 1px solid #90caf9; border-radius: 8px;
  padding: 12px; margin-bottom: 16px;
}
.lx-button-row {
  display: flex; justify-content: center; gap: 8px; margin-bottom: 12px;
}
.lx-control-btn {
  background: #4285f4; color: white; border: none; border-radius: 4px;
  padding: 8px 16px; cursor: pointer; font-size: 13px; font-weight: 500;
  transition: background-color 0.2s;
}
.lx-control-btn:hover { background: #3367d6; }
.lx-progress-container {
  margin-bottom: 8px;
}
.lx-progress-slider {
  width: 100%; margin: 0; appearance: none; height: 6px;
  background: #ddd; border-radius: 3px; outline: none;
}
.lx-progress-slider::-webkit-slider-thumb {
  appearance: none; width: 18px; height: 18px; background: #4285f4;
  border-radius: 50%; cursor: pointer;
}
.lx-progress-slider::-moz-range-thumb {
  width: 18px; height: 18px; background: #4285f4; border-radius: 50%;
  cursor: pointer; border: none;
}
.lx-status-text {
  text-align: center; font-size: 12px; color: #666; margin-top: 4px;
}
.lx-text-window {
  font-family: monospace; white-space: pre-wrap; border: 1px solid #90caf9;
  padding: 12px; max-height: 260px; overflow-y: auto; margin-bottom: 12px;
  line-height: 1.6;
}
.lx-attributes-panel {
  background: #fafafa; border: 1px solid #90caf9; border-radius: 6px;
  padding: 8px 10px; margin-top: 8px; font-size: 13px;
}
.lx-current-highlight {
  border-bottom: 4px solid #ff4444;
  font-weight: bold;
  animation: lx-pulse 1s ease-in-out;
}
@keyframes lx-pulse {
  0% { text-decoration-color: #ff4444; }
  50% { text-decoration-color: #ff0000; }
  100% { text-decoration-color: #ff4444; }
}
.lx-legend {
  font-size: 12px; margin-bottom: 8px;
  padding-bottom: 8px; border-bottom: 1px solid #e0e0e0;
}
.lx-label {
  display: inline-block;
  padding: 2px 4px;
  border-radius: 3px;
  margin-right: 4px;
  color: #000;
}
.lx-attr-key {
  font-weight: 600;
  color: #1565c0;
  letter-spacing: 0.3px;
}
.lx-attr-value {
  font-weight: 400;
  opacity: 0.85;
  letter-spacing: 0.2px;
}

/* Add optimizations with larger fonts and better readability for GIFs */
.lx-gif-optimized .lx-text-window { font-size: 16px; line-height: 1.8; }
.lx-gif-optimized .lx-attributes-panel { font-size: 15px; }
.lx-gif-optimized .lx-current-highlight { text-decoration-thickness: 4px; }
</style>
    <div class="lx-animated-wrapper lx-gif-optimized">
      <div class="lx-attributes-panel">
        <div class="lx-legend">Highlights Legend: <span class="lx-label" style="background-color:#D2E3FC;">product</span></div>
        <div id="attributesContainer"></div>
      </div>
      <div class="lx-text-window" id="textWindow">
        <span class="lx-highlight lx-current-highlight" data-idx="0" style="background-color:#D2E3FC;">Floral Printed Cold Shoulder Top</span>
<span class="lx-highlight" data-idx="1" style="background-color:#D2E3FC;">2018 Strawberry Festival Tee</span>
<span class="lx-highlight" data-idx="2" style="background-color:#D2E3FC;">2018 Strawberry Festival Tee 1XL</span>
<span class="lx-highlight" data-idx="3" style="background-color:#D2E3FC;">Multilayer Sun Dress Tan</span>
<span class="lx-highlight" data-idx="4" style="background-color:#D2E3FC;">Multilayer Sun Dress Pink</span>
<span class="lx-highlight" data-idx="5" style="background-color:#D2E3FC;">Multilayer Sun Dress Coral</span>
<span class="lx-highlight" data-idx="6" style="background-color:#D2E3FC;">Multilayer Top Blue and White Blue</span>
<span class="lx-highlight" data-idx="7" style="background-color:#D2E3FC;">Paisley Print Peasant Top Small</span>
<span class="lx-highlight" data-idx="8" style="background-color:#D2E3FC;">Paisley Print Peasant Top Large</span>
<span class="lx-highlight" data-idx="9" style="background-color:#D2E3FC;">Multilayer Blue Top with Scarf Blue</span>
<span class="lx-highlight" data-idx="10" style="background-color:#D2E3FC;">Multilayer Navy and White Top Navy</span>
<span class="lx-highlight" data-idx="11" style="background-color:#D2E3FC;">Pink Print Cold Shoulder Tunic SMALL</span>
<span class="lx-highlight" data-idx="12" style="background-color:#D2E3FC;">Pink Print Cold Shoulder Tunic MED</span>
<span class="lx-highlight" data-idx="13" style="background-color:#D2E3FC;">Pink Print Cold Shoulder Tunic LARGE</span>
<span class="lx-highlight" data-idx="14" style="background-color:#D2E3FC;">Multilayer Sheer Pant Beige</span>
<span class="lx-highlight" data-idx="15" style="background-color:#D2E3FC;">Multilayer Sheer Pant Navy</span>
<span class="lx-highlight" data-idx="16" style="background-color:#D2E3FC;">Black Print Dress 2XL</span>
<span class="lx-highlight" data-idx="17" style="background-color:#D2E3FC;">Print Dress Red 2XL</span>
<span class="lx-highlight" data-idx="18" style="background-color:#D2E3FC;">Print Dress Red 3XL</span>
<span class="lx-highlight" data-idx="19" style="background-color:#D2E3FC;">Print Dress Blue 2XL</span>
<span class="lx-highlight" data-idx="20" style="background-color:#D2E3FC;">Print Dress Blue 3XL</span>
<span class="lx-highlight" data-idx="21" style="background-color:#D2E3FC;">Knit Tunic 1XL</span>
<span class="lx-highlight" data-idx="22" style="background-color:#D2E3FC;">Knit Tunic 2XL</span>
<span class="lx-highlight" data-idx="23" style="background-color:#D2E3FC;">Knit Tunic 3XL</span>
<span class="lx-highlight" data-idx="24" style="background-color:#D2E3FC;">Print Dress Lavender 1XL</span>
<span class="lx-highlight" data-idx="25" style="background-color:#D2E3FC;">Print Dress Lavender 2XL</span>
<span class="lx-highlight" data-idx="26" style="background-color:#D2E3FC;">Print Dress Lavender 3XL</span>
<span class="lx-highlight" data-idx="27" style="background-color:#D2E3FC;">Print Handkerchief Hem Dress Small</span>
<span class="lx-highlight" data-idx="28" style="background-color:#D2E3FC;">Print Handkerchief Hem Dress Medium</span>
<span class="lx-highlight" data-idx="29" style="background-color:#D2E3FC;">Print Handkerchief Hem Dress Large</span>
<span class="lx-highlight" data-idx="30" style="background-color:#D2E3FC;">Denim Jeans 2XL</span>
      </div>
      <div class="lx-controls">
        <div class="lx-button-row">
          <button class="lx-control-btn" onclick="playPause()">▶️ Play</button>
          <button class="lx-control-btn" onclick="prevExtraction()">⏮ Previous</button>
          <button class="lx-control-btn" onclick="nextExtraction()">⏭ Next</button>
        </div>
        <div class="lx-progress-container">
          <input type="range" id="progressSlider" class="lx-progress-slider"
                 min="0" max="30" value="0"
                 onchange="jumpToExtraction(this.value)">
        </div>
        <div class="lx-status-text">
          Entity <span id="entityInfo">1/31</span> |
          Pos <span id="posInfo">[0-32]</span>
        </div>
      </div>
    </div>

    <script>
      (function() {
        const extractions = [{"index": 0, "class": "product", "text": "Floral Printed Cold Shoulder Top", "color": "#D2E3FC", "startPos": 0, "endPos": 32, "beforeText": "", "extractionText": "Floral Printed Cold Shoulder Top", "afterText": "\n2018 Strawberry Festival Tee\n2018 Strawberry Festival Tee 1XL\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilaye", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Floral Printed Cold Shoulder Top</span>}</div>"}, {"index": 1, "class": "product", "text": "2018 Strawberry Festival Tee", "color": "#D2E3FC", "startPos": 33, "endPos": 61, "beforeText": "Floral Printed Cold Shoulder Top\n", "extractionText": "2018 Strawberry Festival Tee", "afterText": "\n2018 Strawberry Festival Tee 1XL\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\nPai", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Strawberry Festival Tee</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Year</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">2018</span>}</div>"}, {"index": 2, "class": "product", "text": "2018 Strawberry Festival Tee 1XL", "color": "#D2E3FC", "startPos": 62, "endPos": 94, "beforeText": "Floral Printed Cold Shoulder Top\n2018 Strawberry Festival Tee\n", "extractionText": "2018 Strawberry Festival Tee 1XL", "afterText": "\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\nPais", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Strawberry Festival Tee</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Year</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">2018</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">1XL</span>}</div>"}, {"index": 3, "class": "product", "text": "Multilayer Sun Dress Tan", "color": "#D2E3FC", "startPos": 95, "endPos": 119, "beforeText": "Floral Printed Cold Shoulder Top\n2018 Strawberry Festival Tee\n2018 Strawberry Festival Tee 1XL\n", "extractionText": "Multilayer Sun Dress Tan", "afterText": "\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Lar", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Sun Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Tan</span>}</div>"}, {"index": 4, "class": "product", "text": "Multilayer Sun Dress Pink", "color": "#D2E3FC", "startPos": 120, "endPos": 145, "beforeText": "Floral Printed Cold Shoulder Top\n2018 Strawberry Festival Tee\n2018 Strawberry Festival Tee 1XL\nMultilayer Sun Dress Tan\n", "extractionText": "Multilayer Sun Dress Pink", "afterText": "\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Large\nMultilayer Blue Top wit", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Sun Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Pink</span>}</div>"}, {"index": 5, "class": "product", "text": "Multilayer Sun Dress Coral", "color": "#D2E3FC", "startPos": 146, "endPos": 172, "beforeText": "Floral Printed Cold Shoulder Top\n2018 Strawberry Festival Tee\n2018 Strawberry Festival Tee 1XL\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\n", "extractionText": "Multilayer Sun Dress Coral", "afterText": "\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Large\nMultilayer Blue Top with Scarf Blue\nMultilayer Nav", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Sun Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Coral</span>}</div>"}, {"index": 6, "class": "product", "text": "Multilayer Top Blue and White Blue", "color": "#D2E3FC", "startPos": 173, "endPos": 207, "beforeText": "ulder Top\n2018 Strawberry Festival Tee\n2018 Strawberry Festival Tee 1XL\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\n", "extractionText": "Multilayer Top Blue and White Blue", "afterText": "\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Large\nMultilayer Blue Top with Scarf Blue\nMultilayer Navy and White Top Navy\nPink Print Col", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Top</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Blue and White</span>}</div>"}, {"index": 7, "class": "product", "text": "Paisley Print Peasant Top Small", "color": "#D2E3FC", "startPos": 208, "endPos": 239, "beforeText": "Tee\n2018 Strawberry Festival Tee 1XL\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\n", "extractionText": "Paisley Print Peasant Top Small", "afterText": "\nPaisley Print Peasant Top Large\nMultilayer Blue Top with Scarf Blue\nMultilayer Navy and White Top Navy\nPink Print Cold Shoulder Tunic SMALL\nPink Prin", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Paisley Print Peasant Top</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Small</span>}</div>"}, {"index": 8, "class": "product", "text": "Paisley Print Peasant Top Large", "color": "#D2E3FC", "startPos": 240, "endPos": 271, "beforeText": " 1XL\nMultilayer Sun Dress Tan\nMultilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\n", "extractionText": "Paisley Print Peasant Top Large", "afterText": "\nMultilayer Blue Top with Scarf Blue\nMultilayer Navy and White Top Navy\nPink Print Cold Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\nPink P", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Paisley Print Peasant Top</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Large</span>}</div>"}, {"index": 9, "class": "product", "text": "Multilayer Blue Top with Scarf Blue", "color": "#D2E3FC", "startPos": 272, "endPos": 307, "beforeText": "ltilayer Sun Dress Pink\nMultilayer Sun Dress Coral\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Large\n", "extractionText": "Multilayer Blue Top with Scarf Blue", "afterText": "\nMultilayer Navy and White Top Navy\nPink Print Cold Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMulti", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Blue Top with Scarf</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Blue</span>}</div>"}, {"index": 10, "class": "product", "text": "Multilayer Navy and White Top Navy", "color": "#D2E3FC", "startPos": 308, "endPos": 342, "beforeText": "un Dress Coral\nMultilayer Top Blue and White Blue\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Large\nMultilayer Blue Top with Scarf Blue\n", "extractionText": "Multilayer Navy and White Top Navy", "afterText": "\nPink Print Cold Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer S", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Top</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Navy and White</span>}</div>"}, {"index": 11, "class": "product", "text": "Pink Print Cold Shoulder Tunic SMALL", "color": "#D2E3FC", "startPos": 343, "endPos": 379, "beforeText": "and White Blue\nPaisley Print Peasant Top Small\nPaisley Print Peasant Top Large\nMultilayer Blue Top with Scarf Blue\nMultilayer Navy and White Top Navy\n", "extractionText": "Pink Print Cold Shoulder Tunic SMALL", "afterText": "\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\n", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Pink Print Cold Shoulder Tunic</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">SMALL</span>}</div>"}, {"index": 12, "class": "product", "text": "Pink Print Cold Shoulder Tunic MED", "color": "#D2E3FC", "startPos": 380, "endPos": 414, "beforeText": "Top Small\nPaisley Print Peasant Top Large\nMultilayer Blue Top with Scarf Blue\nMultilayer Navy and White Top Navy\nPink Print Cold Shoulder Tunic SMALL\n", "extractionText": "Pink Print Cold Shoulder Tunic MED", "afterText": "\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Pink Print Cold Shoulder Tunic</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">MED</span>}</div>"}, {"index": 13, "class": "product", "text": "Pink Print Cold Shoulder Tunic LARGE", "color": "#D2E3FC", "startPos": 415, "endPos": 451, "beforeText": " Large\nMultilayer Blue Top with Scarf Blue\nMultilayer Navy and White Top Navy\nPink Print Cold Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\n", "extractionText": "Pink Print Cold Shoulder Tunic LARGE", "afterText": "\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Pink Print Cold Shoulder Tunic</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">LARGE</span>}</div>"}, {"index": 14, "class": "product", "text": "Multilayer Sheer Pant Beige", "color": "#D2E3FC", "startPos": 452, "endPos": 479, "beforeText": " Blue\nMultilayer Navy and White Top Navy\nPink Print Cold Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\n", "extractionText": "Multilayer Sheer Pant Beige", "afterText": "\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKni", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Sheer Pant</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Beige</span>}</div>"}, {"index": 15, "class": "product", "text": "Multilayer Sheer Pant Navy", "color": "#D2E3FC", "startPos": 480, "endPos": 506, "beforeText": "ite Top Navy\nPink Print Cold Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\n", "extractionText": "Multilayer Sheer Pant Navy", "afterText": "\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\n", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Multilayer Sheer Pant</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Navy</span>}</div>"}, {"index": 16, "class": "product", "text": "Black Print Dress 2XL", "color": "#D2E3FC", "startPos": 507, "endPos": 528, "beforeText": "d Shoulder Tunic SMALL\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\n", "extractionText": "Black Print Dress 2XL", "afterText": "\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Black</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">2XL</span>}</div>"}, {"index": 17, "class": "product", "text": "Print Dress Red 2XL", "color": "#D2E3FC", "startPos": 529, "endPos": 548, "beforeText": "\nPink Print Cold Shoulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\n", "extractionText": "Print Dress Red 2XL", "afterText": "\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Laven", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Red</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">2XL</span>}</div>"}, {"index": 18, "class": "product", "text": "Print Dress Red 3XL", "color": "#D2E3FC", "startPos": 549, "endPos": 568, "beforeText": "ulder Tunic MED\nPink Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\n", "extractionText": "Print Dress Red 3XL", "afterText": "\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress ", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Red</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">3XL</span>}</div>"}, {"index": 19, "class": "product", "text": "Print Dress Blue 2XL", "color": "#D2E3FC", "startPos": 569, "endPos": 589, "beforeText": " Print Cold Shoulder Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\n", "extractionText": "Print Dress Blue 2XL", "afterText": "\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Ha", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Blue</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">2XL</span>}</div>"}, {"index": 20, "class": "product", "text": "Print Dress Blue 3XL", "color": "#D2E3FC", "startPos": 590, "endPos": 610, "beforeText": "Tunic LARGE\nMultilayer Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\n", "extractionText": "Print Dress Blue 3XL", "afterText": "\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress ", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Blue</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">3XL</span>}</div>"}, {"index": 21, "class": "product", "text": "Knit Tunic 1XL", "color": "#D2E3FC", "startPos": 611, "endPos": 625, "beforeText": "r Sheer Pant Beige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\n", "extractionText": "Knit Tunic 1XL", "afterText": "\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Han", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Knit Tunic</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">1XL</span>}</div>"}, {"index": 22, "class": "product", "text": "Knit Tunic 2XL", "color": "#D2E3FC", "startPos": 626, "endPos": 640, "beforeText": "ige\nMultilayer Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\n", "extractionText": "Knit Tunic 2XL", "afterText": "\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem D", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Knit Tunic</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">2XL</span>}</div>"}, {"index": 23, "class": "product", "text": "Knit Tunic 3XL", "color": "#D2E3FC", "startPos": 641, "endPos": 655, "beforeText": "Sheer Pant Navy\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\n", "extractionText": "Knit Tunic 3XL", "afterText": "\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem Dress Medium\nPri", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Knit Tunic</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">3XL</span>}</div>"}, {"index": 24, "class": "product", "text": "Print Dress Lavender 1XL", "color": "#D2E3FC", "startPos": 656, "endPos": 680, "beforeText": "\nBlack Print Dress 2XL\nPrint Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\n", "extractionText": "Print Dress Lavender 1XL", "afterText": "\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem Dress Medium\nPrint Handkerchief Hem Dress", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Lavender</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">1XL</span>}</div>"}, {"index": 25, "class": "product", "text": "Print Dress Lavender 2XL", "color": "#D2E3FC", "startPos": 681, "endPos": 705, "beforeText": "int Dress Red 2XL\nPrint Dress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\n", "extractionText": "Print Dress Lavender 2XL", "afterText": "\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem Dress Medium\nPrint Handkerchief Hem Dress Large\nDenim Jeans 2XL", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Lavender</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">2XL</span>}</div>"}, {"index": 26, "class": "product", "text": "Print Dress Lavender 3XL", "color": "#D2E3FC", "startPos": 706, "endPos": 730, "beforeText": "ress Red 3XL\nPrint Dress Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\n", "extractionText": "Print Dress Lavender 3XL", "afterText": "\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem Dress Medium\nPrint Handkerchief Hem Dress Large\nDenim Jeans 2XL", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Color</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Lavender</span>, <span class=\"lx-attr-key\">option2_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option2_value</span>: <span class=\"lx-attr-value\">3XL</span>}</div>"}, {"index": 27, "class": "product", "text": "Print Handkerchief Hem Dress Small", "color": "#D2E3FC", "startPos": 731, "endPos": 765, "beforeText": "Blue 2XL\nPrint Dress Blue 3XL\nKnit Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\n", "extractionText": "Print Handkerchief Hem Dress Small", "afterText": "\nPrint Handkerchief Hem Dress Medium\nPrint Handkerchief Hem Dress Large\nDenim Jeans 2XL", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Handkerchief Hem Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Small</span>}</div>"}, {"index": 28, "class": "product", "text": "Print Handkerchief Hem Dress Medium", "color": "#D2E3FC", "startPos": 766, "endPos": 801, "beforeText": "Tunic 1XL\nKnit Tunic 2XL\nKnit Tunic 3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\n", "extractionText": "Print Handkerchief Hem Dress Medium", "afterText": "\nPrint Handkerchief Hem Dress Large\nDenim Jeans 2XL", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Handkerchief Hem Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Medium</span>}</div>"}, {"index": 29, "class": "product", "text": "Print Handkerchief Hem Dress Large", "color": "#D2E3FC", "startPos": 802, "endPos": 836, "beforeText": "3XL\nPrint Dress Lavender 1XL\nPrint Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem Dress Medium\n", "extractionText": "Print Handkerchief Hem Dress Large", "afterText": "\nDenim Jeans 2XL", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Print Handkerchief Hem Dress</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">Large</span>}</div>"}, {"index": 30, "class": "product", "text": "Denim Jeans 2XL", "color": "#D2E3FC", "startPos": 837, "endPos": 852, "beforeText": "Dress Lavender 2XL\nPrint Dress Lavender 3XL\nPrint Handkerchief Hem Dress Small\nPrint Handkerchief Hem Dress Medium\nPrint Handkerchief Hem Dress Large\n", "extractionText": "Denim Jeans 2XL", "afterText": "", "attributesHtml": "<div><strong>class:</strong> product</div><div><strong>attributes:</strong> {<span class=\"lx-attr-key\">base_product_name</span>: <span class=\"lx-attr-value\">Denim Jeans</span>, <span class=\"lx-attr-key\">option1_name</span>: <span class=\"lx-attr-value\">Size</span>, <span class=\"lx-attr-key\">option1_value</span>: <span class=\"lx-attr-value\">2XL</span>}</div>"}];
        let currentIndex = 0;
        let isPlaying = false;
        let animationInterval = null;
        let animationSpeed = 1.0;

        function updateDisplay() {
          const extraction = extractions[currentIndex];
          if (!extraction) return;

          document.getElementById('attributesContainer').innerHTML = extraction.attributesHtml;
          document.getElementById('entityInfo').textContent = (currentIndex + 1) + '/' + extractions.length;
          document.getElementById('posInfo').textContent = '[' + extraction.startPos + '-' + extraction.endPos + ']';
          document.getElementById('progressSlider').value = currentIndex;

          const playBtn = document.querySelector('.lx-control-btn');
          if (playBtn) playBtn.textContent = isPlaying ? '⏸ Pause' : '▶️ Play';

          const prevHighlight = document.querySelector('.lx-text-window .lx-current-highlight');
          if (prevHighlight) prevHighlight.classList.remove('lx-current-highlight');
          const currentSpan = document.querySelector('.lx-text-window span[data-idx="' + currentIndex + '"]');
          if (currentSpan) {
            currentSpan.classList.add('lx-current-highlight');
            currentSpan.scrollIntoView({block: 'center', behavior: 'smooth'});
          }
        }

        function nextExtraction() {
          currentIndex = (currentIndex + 1) % extractions.length;
          updateDisplay();
        }

        function prevExtraction() {
          currentIndex = (currentIndex - 1 + extractions.length) % extractions.length;
          updateDisplay();
        }

        function jumpToExtraction(index) {
          currentIndex = parseInt(index);
          updateDisplay();
        }

        function playPause() {
          if (isPlaying) {
            clearInterval(animationInterval);
            isPlaying = false;
          } else {
            animationInterval = setInterval(nextExtraction, animationSpeed * 1000);
            isPlaying = true;
          }
          updateDisplay();
        }

        window.playPause = playPause;
        window.nextExtraction = nextExtraction;
        window.prevExtraction = prevExtraction;
        window.jumpToExtraction = jumpToExtraction;

        updateDisplay();
      })();
    </script>