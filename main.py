#!/usr/bin/env python3
"""
Production-ready LangExtract Product Variant Extraction Application

This application processes product names from CSV files and extracts
base product names and option variants using LangExtract.
"""

import os
import sys
import csv
import logging
import textwrap
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse

import langextract as lx
import pandas as pd
from dotenv import load_dotenv


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('langextract_app.log')
    ]
)
logger = logging.getLogger(__name__)


class ProductExtractor:
    """Main class for product variant extraction using LangExtract."""

    def __init__(self, api_key: str, model_id: str = "gemini-2.5-flash", enable_html_output: bool = False):
        """
        Initialize the ProductExtractor.

        Args:
            api_key: API key for the LangExtract service
            model_id: Model ID to use for extraction
            enable_html_output: Whether to generate HTML visualization output
        """
        self.api_key = api_key
        self.model_id = model_id
        self.enable_html_output = enable_html_output
        self.prompt_description = textwrap.dedent("""\
            Extract the base product name and any option name/value pairs (such as color, size, material, model, etc.)
            from each product name string. For each product, output a single extraction with attributes:
            base_product_name, option1_name, option1_value, option2_name, option2_value, etc.
        """)

        # Set up the API key
        os.environ["LANGEXTRACT_API_KEY"] = self.api_key

        # Initialize example data for training
        self.examples = self._create_example_data()

        # Store the last extraction result for visualization
        self.last_extraction_result = None
        
    def _create_example_data(self) -> List[lx.data.ExampleData]:
        """Create example data for training the extraction model."""
        sample_data = [
            ("Classic Tee Blue M", {"base_product_name":"Classic Tee", "option1_name":"Color", "option1_value":"Blue", "option2_name":"Size", "option2_value":"M"}),
            ("Sneaker Airmax White 10 Men", {"base_product_name":"Sneaker Airmax", "option1_name":"Color", "option1_value":"White", "option2_name":"Size", "option2_value":"10", "option3_name":"Gender", "option3_value":"Men"}),
            ("Diamond Ring Platinum Ruby", {"base_product_name":"Diamond Ring", "option1_name":"Metal", "option1_value":"Platinum", "option2_name":"Stone", "option2_value":"Ruby"}),
            ("Hybrid Bike Large Red 18-speed", {"base_product_name":"Hybrid Bike", "option1_name":"Frame Size", "option1_value":"Large", "option2_name":"Color", "option2_value":"Red", "option3_name":"Gear Count", "option3_value":"18"}),
            ("Dress Shirt Slim White L", {"base_product_name":"Dress Shirt", "option1_name":"Fit", "option1_value":"Slim", "option2_name":"Color", "option2_value":"White", "option3_name":"Size", "option3_value":"L"}),
            ("Sofa 3-Seater Blue Velvet", {"base_product_name":"Sofa", "option1_name":"Seater", "option1_value":"3-Seater", "option2_name":"Color", "option2_value":"Blue", "option3_name":"Material", "option3_value":"Velvet"}),
            ("Perfume Blossom Eau de Parfum 50ml", {"base_product_name":"Perfume", "option1_name":"Fragrance", "option1_value":"Blossom", "option2_name":"Type", "option2_value":"Eau de Parfum", "option3_name":"Volume", "option3_value":"50ml"}),
            ("Yoga Mat Eco 8mm Purple", {"base_product_name":"Yoga Mat", "option1_name":"Material", "option1_value":"Eco", "option2_name":"Thickness", "option2_value":"8mm", "option3_name":"Color", "option3_value":"Purple"}),
            ("Laptop Pro 16GB 512GB 15-inch", {"base_product_name":"Laptop Pro", "option1_name":"RAM", "option1_value":"16GB", "option2_name":"Storage", "option2_value":"512GB", "option3_name":"Screen Size", "option3_value":"15-inch"}),
            ("Wireless Mouse Pro Black", {"base_product_name":"Wireless Mouse", "option1_name":"Model", "option1_value":"Pro", "option2_name":"Color", "option2_value":"Black"}),
        ]
        
        return [
            lx.data.ExampleData(
                text=name,
                extractions=[
                    lx.data.Extraction(
                        extraction_class="product",
                        extraction_text=name,
                        attributes=attrs
                    )
                ]
            )
            for name, attrs in sample_data
        ]
    
    def read_product_names_from_csv(self, csv_file_path: str, column_name: str = "product_name") -> List[str]:
        """
        Read product names from a CSV file.
        
        Args:
            csv_file_path: Path to the CSV file
            column_name: Name of the column containing product names
            
        Returns:
            List of product names
        """
        try:
            df = pd.read_csv(csv_file_path)
            if column_name not in df.columns:
                raise ValueError(f"Column '{column_name}' not found in CSV. Available columns: {list(df.columns)}")
            
            product_names = df[column_name].dropna().astype(str).tolist()
            logger.info(f"Successfully read {len(product_names)} product names from {csv_file_path}")
            return product_names
            
        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file_path}: {str(e)}")
            raise
    
    def extract_variants(self, product_names: List[str], extraction_passes: int = 1) -> List[Dict[str, Any]]:
        """
        Extract product variants from a list of product names.
        
        Args:
            product_names: List of product names to process
            extraction_passes: Number of extraction passes to perform
            
        Returns:
            List of dictionaries containing extraction results
        """
        try:
            logger.info(f"Starting extraction for {len(product_names)} products using model {self.model_id}")
            
            # Run the extraction
            result = lx.extract(
                text_or_documents="\n".join(product_names),
                prompt_description=self.prompt_description,
                examples=self.examples,
                model_id=self.model_id,
                extraction_passes=extraction_passes
            )

            # Store the result for potential HTML visualization
            self.last_extraction_result = result

            # Post-process the extractions into structured data
            rows = []
            max_options = 0

            for ext in result.extractions:
                attrs = ext.attributes or {}
                row = {
                    "OriginalProductName": ext.extraction_text,
                    "BaseProductName": attrs.get("base_product_name", "")
                }

                # Gather option pairs
                i = 1
                while True:
                    name_key = f"option{i}_name"
                    value_key = f"option{i}_value"
                    if name_key in attrs and value_key in attrs:
                        row[f"Option{i}Name"] = attrs[name_key]
                        row[f"Option{i}Value"] = attrs[value_key]
                        i += 1
                    else:
                        break

                max_options = max(max_options, i - 1)
                rows.append(row)

            logger.info(f"Successfully extracted variants for {len(rows)} products with max {max_options} options")
            return rows, max_options
            
        except Exception as e:
            logger.error(f"Error during extraction: {str(e)}")
            raise
    
    def save_results_to_csv(self, results: List[Dict[str, Any]], max_options: int, output_file: str):
        """
        Save extraction results to a CSV file.
        
        Args:
            results: List of extraction results
            max_options: Maximum number of options found
            output_file: Path to the output CSV file
        """
        try:
            # Prepare CSV header
            header = ["OriginalProductName", "BaseProductName"]
            for i in range(1, max_options + 1):
                header += [f"Option{i}Name", f"Option{i}Value"]
            
            # Write to CSV
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(header)
                
                for row in results:
                    writer.writerow([row.get(col, "") for col in header])
            
            logger.info(f"Results saved to {output_file}")
            
        except Exception as e:
            logger.error(f"Error saving results to CSV: {str(e)}")
            raise

    def generate_html_visualization(self, output_dir: str = ".", html_filename: str = "visualization.html",
                                  jsonl_filename: str = "extraction_results.jsonl") -> Optional[str]:
        """
        Generate HTML visualization from the last extraction result.

        Args:
            output_dir: Directory to save the output files
            html_filename: Name of the HTML output file
            jsonl_filename: Name of the intermediate JSONL file

        Returns:
            Path to the generated HTML file, or None if visualization is disabled or no results available
        """
        if not self.enable_html_output:
            logger.info("HTML visualization is disabled")
            return None

        if not self.last_extraction_result:
            logger.warning("No extraction results available for visualization")
            return None

        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            jsonl_path = output_path / jsonl_filename
            html_path = output_path / html_filename

            logger.info(f"Generating HTML visualization...")

            # Save annotated documents to JSONL file
            lx.io.save_annotated_documents([self.last_extraction_result],
                                         output_name=jsonl_filename,
                                         output_dir=str(output_path))

            # Generate HTML visualization from the JSONL file
            html_content = lx.visualize(str(jsonl_path))

            # Write HTML content to file
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)

            logger.info(f"HTML visualization saved to {html_path}")
            return str(html_path)

        except Exception as e:
            logger.error(f"Error generating HTML visualization: {str(e)}")
            raise

    def cleanup_intermediate_files(self, output_dir: str = ".", jsonl_filename: str = "extraction_results.jsonl"):
        """
        Clean up intermediate JSONL files if desired.

        Args:
            output_dir: Directory containing the files
            jsonl_filename: Name of the JSONL file to remove
        """
        try:
            jsonl_path = Path(output_dir) / jsonl_filename
            if jsonl_path.exists():
                jsonl_path.unlink()
                logger.debug(f"Cleaned up intermediate file: {jsonl_path}")
        except Exception as e:
            logger.warning(f"Could not clean up intermediate file: {str(e)}")


def main():
    """Main application entry point."""
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract product variants using LangExtract")
    parser.add_argument("--input-csv", required=True, help="Path to input CSV file containing product names")
    parser.add_argument("--output-csv", default="extracted_variants.csv", help="Path to output CSV file")
    parser.add_argument("--column-name", default="product_name", help="Name of the column containing product names")
    parser.add_argument("--model-id", default="gemini-2.5-flash", help="Model ID to use for extraction")
    parser.add_argument("--extraction-passes", type=int, default=1, help="Number of extraction passes")

    # HTML visualization options
    parser.add_argument("--enable-html", action="store_true", help="Enable HTML visualization output")
    parser.add_argument("--html-output", default="visualization.html", help="Path to HTML visualization output file")
    parser.add_argument("--output-dir", default=".", help="Directory for all output files (CSV and HTML)")
    parser.add_argument("--keep-jsonl", action="store_true", help="Keep intermediate JSONL file (for debugging)")
    
    args = parser.parse_args()
    
    # Get API key from environment
    api_key = os.getenv("LANGEXTRACT_API_KEY")
    if not api_key:
        logger.error("LANGEXTRACT_API_KEY environment variable is required")
        sys.exit(1)
    
    try:
        # Ensure output directory exists
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Adjust output paths to use the specified output directory
        csv_output_path = output_dir / Path(args.output_csv).name
        html_output_path = output_dir / Path(args.html_output).name

        # Initialize the extractor with HTML visualization option
        extractor = ProductExtractor(
            api_key=api_key,
            model_id=args.model_id,
            enable_html_output=args.enable_html
        )

        # Read product names from CSV
        product_names = extractor.read_product_names_from_csv(args.input_csv, args.column_name)

        if not product_names:
            logger.warning("No product names found in the input file")
            return

        # Extract variants
        results, max_options = extractor.extract_variants(product_names, args.extraction_passes)

        # Save CSV results
        extractor.save_results_to_csv(results, max_options, str(csv_output_path))

        # Generate HTML visualization if enabled
        if args.enable_html:
            html_path = extractor.generate_html_visualization(
                output_dir=str(output_dir),
                html_filename=html_output_path.name
            )
            if html_path:
                logger.info(f"HTML visualization available at: {html_path}")

            # Clean up intermediate JSONL file unless requested to keep it
            if not args.keep_jsonl:
                extractor.cleanup_intermediate_files(output_dir=str(output_dir))

        logger.info("Processing completed successfully!")
        logger.info(f"CSV output: {csv_output_path}")
        if args.enable_html:
            logger.info(f"HTML output: {html_output_path}")

    except Exception as e:
        logger.error(f"Application failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
