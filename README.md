# LangExtract Product Variant Extraction Application

A production-ready application that extracts product variants from CSV files using LangExtract. This application processes product names and extracts base product names along with option variants (color, size, material, etc.) in a structured format.

## Features

- **CSV Input Processing**: Read product names from CSV files
- **Intelligent Extraction**: Uses LangExtract with pre-trained examples for accurate variant extraction
- **Structured Output**: Exports results to CSV with organized columns for base products and options
- **Docker Support**: Fully containerized with volume mounting for data files
- **Environment Configuration**: Secure API key management with .env files
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Latest Python**: Built with Python 3.12 and latest package versions

## Quick Start

### Prerequisites

- Python 3.12+ (for local development)
- Docker and Docker Compose (for containerized deployment)
- LangExtract API key

### 1. Clone and Setup

```bash
git clone <your-repo>
cd langextract
```

### 2. Environment Configuration

```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your API key
LANGEXTRACT_API_KEY=your_actual_api_key_here
```

### 3. Prepare Your Data

Place your CSV file in the `data/input/` directory. The CSV should have a column containing product names (default column name: `product_name`).

Example CSV format:
```csv
product_name
Classic Tee Blue M
Sneaker Airmax White 10 Men
Diamond Ring Platinum Ruby
```

## Usage

### Docker Deployment (Recommended)

#### Option 1: Using Docker Compose

```bash
# Build and run the application
docker-compose up --build

# For custom CSV files or parameters
docker-compose run --rm langextract-app \
  --input-csv /data/input/your_products.csv \
  --output-csv /data/output/your_results.csv \
  --column-name your_column_name
```

#### Option 2: Using Docker directly

```bash
# Build the image
docker build -t langextract-app .

# Run with volume mounts
docker run --rm \
  -e LANGEXTRACT_API_KEY=your_api_key \
  -v $(pwd)/data/input:/data/input:ro \
  -v $(pwd)/data/output:/data/output:rw \
  langextract-app \
  --input-csv /data/input/products.csv \
  --output-csv /data/output/extracted_variants.csv
```

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py \
  --input-csv data/input/products.csv \
  --output-csv data/output/extracted_variants.csv \
  --column-name product_name
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--input-csv` | Path to input CSV file | Required |
| `--output-csv` | Path to output CSV file | `extracted_variants.csv` |
| `--column-name` | Column name containing product names | `product_name` |
| `--model-id` | LangExtract model to use | `gemini-2.5-flash` |
| `--extraction-passes` | Number of extraction passes | `1` |

## Output Format

The application generates a CSV file with the following structure:

| Column | Description |
|--------|-------------|
| `OriginalProductName` | Original product name from input |
| `BaseProductName` | Extracted base product name |
| `Option1Name` | Name of first option (e.g., "Color") |
| `Option1Value` | Value of first option (e.g., "Blue") |
| `Option2Name` | Name of second option (e.g., "Size") |
| `Option2Value` | Value of second option (e.g., "M") |
| ... | Additional option pairs as needed |

Example output:
```csv
OriginalProductName,BaseProductName,Option1Name,Option1Value,Option2Name,Option2Value
Classic Tee Blue M,Classic Tee,Color,Blue,Size,M
Sneaker Airmax White 10 Men,Sneaker Airmax,Color,White,Size,10,Gender,Men
```

## Directory Structure

```
langextract/
├── main.py                 # Main application script
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose configuration
├── .env.example          # Environment template
├── .dockerignore         # Docker ignore file
├── sample_products.csv   # Sample input data
├── data/
│   ├── input/           # Input CSV files (mounted in Docker)
│   └── output/          # Output CSV files (mounted in Docker)
├── logs/                # Application logs
└── README.md           # This file
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `LANGEXTRACT_API_KEY` | API key for LangExtract service | Yes |
| `LANGEXTRACT_MODEL_ID` | Model ID to use | No (default: gemini-2.5-flash) |

## Development

### Adding New Example Data

To improve extraction accuracy, you can add more training examples in the `_create_example_data()` method in `main.py`.

### Customizing Extraction Logic

The extraction prompt can be modified in the `prompt_description` attribute of the `ProductExtractor` class.

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your `LANGEXTRACT_API_KEY` is set correctly in the `.env` file
2. **CSV Column Not Found**: Verify the column name matches your CSV file structure
3. **Permission Errors**: Ensure the `data/output` directory is writable

### Logs

Application logs are written to:
- Console output (stdout)
- `langextract_app.log` file
- `logs/` directory when using Docker

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
