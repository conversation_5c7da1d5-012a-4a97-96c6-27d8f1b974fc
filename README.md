# LangExtract Product Variant Extraction Application

A production-ready application that extracts product variants from CSV files using LangExtract. This application processes product names and extracts base product names along with option variants (color, size, material, etc.) in a structured format.

## Features

- **CSV Input Processing**: Read product names from CSV files
- **Intelligent Extraction**: Uses LangExtract with pre-trained examples for accurate variant extraction
- **Dual Output Formats**:
  - **CSV Export**: Structured data with organized columns for base products and options
  - **HTML Visualization**: Interactive visual representation of extraction results with highlighting
- **Docker Support**: Fully containerized with volume mounting for data files
- **Environment Configuration**: Secure API key management with .env files
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Latest Python**: Built with Python 3.12 and latest package versions

## Quick Start

### Prerequisites

- Python 3.12+ (for local development)
- Docker and Docker Compose (for containerized deployment)
- LangExtract API key

### 1. Clone and Setup

```bash
git clone <your-repo>
cd langextract
```

### 2. Environment Configuration

```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your API key
LANGEXTRACT_API_KEY=your_actual_api_key_here
```

### 3. Prepare Your Data

Place your CSV file in the `data/input/` directory. The CSV should have a column containing product names (default column name: `product_name`).

Example CSV format:
```csv
product_name
Classic Tee Blue M
Sneaker Airmax White 10 Men
Diamond Ring Platinum Ruby
```

## Usage

### Docker Deployment (Recommended)

#### Option 1: Using Docker Compose

```bash
# Build and run with HTML visualization (default)
docker-compose up --build

# Run CSV-only processing (no HTML visualization)
docker-compose --profile csv-only up --build

# For custom CSV files or parameters
docker-compose run --rm langextract-app \
  --input-csv /data/input/your_products.csv \
  --output-csv your_results.csv \
  --output-dir /data/output \
  --enable-html \
  --html-output your_visualization.html \
  --column-name your_column_name
```

#### Option 2: Using Docker directly

```bash
# Build the image
docker build -t langextract-app .

# Run with HTML visualization
docker run --rm \
  -e LANGEXTRACT_API_KEY=your_api_key \
  -v $(pwd)/data/input:/data/input:ro \
  -v $(pwd)/data/output:/data/output:rw \
  langextract-app \
  --input-csv /data/input/products.csv \
  --output-dir /data/output \
  --enable-html \
  --html-output visualization.html

# Run CSV-only (no HTML visualization)
docker run --rm \
  -e LANGEXTRACT_API_KEY=your_api_key \
  -v $(pwd)/data/input:/data/input:ro \
  -v $(pwd)/data/output:/data/output:rw \
  langextract-app \
  --input-csv /data/input/products.csv \
  --output-csv extracted_variants.csv \
  --output-dir /data/output
```

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run with HTML visualization
python main.py \
  --input-csv data/input/products.csv \
  --output-dir data/output \
  --enable-html \
  --html-output visualization.html \
  --column-name product_name

# Run CSV-only
python main.py \
  --input-csv data/input/products.csv \
  --output-csv extracted_variants.csv \
  --output-dir data/output \
  --column-name product_name
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--input-csv` | Path to input CSV file | Required |
| `--output-csv` | Path to output CSV file | `extracted_variants.csv` |
| `--column-name` | Column name containing product names | `product_name` |
| `--model-id` | LangExtract model to use | `gemini-2.5-flash` |
| `--extraction-passes` | Number of extraction passes | `1` |
| `--enable-html` | Enable HTML visualization output | `False` |
| `--html-output` | Path to HTML visualization file | `visualization.html` |
| `--output-dir` | Directory for all output files | `.` |
| `--keep-jsonl` | Keep intermediate JSONL file | `False` |

## Output Formats

The application generates two types of output:

### CSV Output

A structured CSV file with the following columns:

| Column | Description |
|--------|-------------|
| `OriginalProductName` | Original product name from input |
| `BaseProductName` | Extracted base product name |
| `Option1Name` | Name of first option (e.g., "Color") |
| `Option1Value` | Value of first option (e.g., "Blue") |
| `Option2Name` | Name of second option (e.g., "Size") |
| `Option2Value` | Value of second option (e.g., "M") |
| ... | Additional option pairs as needed |

Example CSV output:
```csv
OriginalProductName,BaseProductName,Option1Name,Option1Value,Option2Name,Option2Value
Classic Tee Blue M,Classic Tee,Color,Blue,Size,M
Sneaker Airmax White 10 Men,Sneaker Airmax,Color,White,Size,10,Gender,Men
```

### HTML Visualization (Optional)

When `--enable-html` is used, the application also generates an interactive HTML file that provides:

- **Visual Highlighting**: Product names with color-coded extraction highlights
- **Interactive Tooltips**: Hover over highlighted text to see extracted attributes
- **Playback Controls**: Step through the extraction process with animation controls
- **Detailed View**: Complete breakdown of all extracted entities and their attributes

The HTML visualization is perfect for:
- **Quality Review**: Visually inspect extraction accuracy
- **Presentation**: Share results with stakeholders in an accessible format
- **Debugging**: Understand how the model interpreted each product name
- **Training**: Use as examples for improving extraction prompts

## HTML Visualization Workflow

The HTML visualization feature uses LangExtract's built-in visualization capabilities:

1. **Extraction**: The application processes your CSV data using LangExtract
2. **Annotation**: Results are saved to an intermediate JSONL file with detailed annotations
3. **Visualization**: LangExtract generates an interactive HTML file from the annotations
4. **Cleanup**: Intermediate files are automatically removed (unless `--keep-jsonl` is specified)

### Viewing HTML Results

After running with `--enable-html`, open the generated HTML file in any web browser:

```bash
# Open the visualization file
open data/output/visualization.html  # macOS
xdg-open data/output/visualization.html  # Linux
start data/output/visualization.html  # Windows
```

The HTML file is self-contained and can be shared or viewed offline.

## Directory Structure

```
langextract/
├── main.py                 # Main application script
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose configuration
├── .env.example          # Environment template
├── .dockerignore         # Docker ignore file
├── sample_products.csv   # Sample input data
├── data/
│   ├── input/           # Input CSV files (mounted in Docker)
│   └── output/          # Output files (CSV + HTML, mounted in Docker)
├── logs/                # Application logs
└── README.md           # This file
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `LANGEXTRACT_API_KEY` | API key for LangExtract service | Yes |
| `LANGEXTRACT_MODEL_ID` | Model ID to use | No (default: gemini-2.5-flash) |

## Development

### Adding New Example Data

To improve extraction accuracy, you can add more training examples in the `_create_example_data()` method in `main.py`.

### Customizing Extraction Logic

The extraction prompt can be modified in the `prompt_description` attribute of the `ProductExtractor` class.

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your `LANGEXTRACT_API_KEY` is set correctly in the `.env` file
2. **CSV Column Not Found**: Verify the column name matches your CSV file structure
3. **Permission Errors**: Ensure the `data/output` directory is writable
4. **HTML Not Generated**: Check that `--enable-html` flag is used and no errors occurred during extraction
5. **HTML File Empty/Corrupted**: Ensure the extraction completed successfully before HTML generation

### Example: Complete Workflow

```bash
# 1. Prepare your data
echo "product_name" > data/input/my_products.csv
echo "Wireless Bluetooth Headphones Black Over-Ear" >> data/input/my_products.csv
echo "Gaming Laptop 16GB RAM 1TB SSD 15-inch" >> data/input/my_products.csv

# 2. Run extraction with both CSV and HTML output
docker-compose up

# 3. View results
cat data/output/extracted_variants.csv
open data/output/visualization.html
```

This will generate:
- **CSV**: Structured data ready for analysis or database import
- **HTML**: Interactive visualization for review and presentation

### Logs

Application logs are written to:
- Console output (stdout)
- `langextract_app.log` file
- `logs/` directory when using Docker

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
